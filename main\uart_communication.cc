#include "uart_communication.h"
#include <cstring>

#define TAG "UartCommunication"

UartCommunication::UartCommunication(uart_port_t uart_num, gpio_num_t tx_pin, gpio_num_t rx_pin, 
                                   int baud_rate, int buffer_size)
    : uart_num_(uart_num), tx_pin_(tx_pin), rx_pin_(rx_pin), 
      baud_rate_(baud_rate), buffer_size_(buffer_size), 
      initialized_(false), receive_task_handle_(nullptr) {
}

UartCommunication::~UartCommunication() {
    StopReceiveTask();
    if (initialized_) {
        uart_driver_delete(uart_num_);
    }
}

bool UartCommunication::Initialize() {
    if (initialized_) {
        ESP_LOGW(TAG, "UART already initialized");
        return true;
    }

    // 配置UART参数
    uart_config_t uart_config = {
        .baud_rate = baud_rate_,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .rx_flow_ctrl_thresh = 122,
        .source_clk = UART_SCLK_DEFAULT,
    };

    // 安装UART驱动
    esp_err_t ret = uart_driver_install(uart_num_, buffer_size_ * 2, 0, 0, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(ret));
        return false;
    }

    // 配置UART参数
    ret = uart_param_config(uart_num_, &uart_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure UART parameters: %s", esp_err_to_name(ret));
        uart_driver_delete(uart_num_);
        return false;
    }

    // 设置UART引脚
    ret = uart_set_pin(uart_num_, tx_pin_, rx_pin_, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(ret));
        uart_driver_delete(uart_num_);
        return false;
    }

    initialized_ = true;
    ESP_LOGI(TAG, "UART%d initialized successfully (TX: %d, RX: %d, Baud: %d)", 
             uart_num_, tx_pin_, rx_pin_, baud_rate_);
    
    return true;
}

bool UartCommunication::SendString(const std::string& data) {
    if (!initialized_) {
        ESP_LOGE(TAG, "UART not initialized");
        return false;
    }

    std::lock_guard<std::mutex> lock(mutex_);
    
    int bytes_written = uart_write_bytes(uart_num_, data.c_str(), data.length());
    if (bytes_written < 0) {
        ESP_LOGE(TAG, "Failed to write to UART");
        return false;
    }

    ESP_LOGD(TAG, "Sent: %s", data.c_str());
    return true;
}

bool UartCommunication::SendData(const uint8_t* data, size_t length) {
    if (!initialized_) {
        ESP_LOGE(TAG, "UART not initialized");
        return false;
    }

    std::lock_guard<std::mutex> lock(mutex_);
    
    int bytes_written = uart_write_bytes(uart_num_, data, length);
    if (bytes_written < 0) {
        ESP_LOGE(TAG, "Failed to write to UART");
        return false;
    }

    return true;
}

void UartCommunication::SetReceiveCallback(std::function<void(const std::string&)> callback) {
    receive_callback_ = callback;
}

bool UartCommunication::StartReceiveTask() {
    if (!initialized_) {
        ESP_LOGE(TAG, "UART not initialized");
        return false;
    }

    if (receive_task_handle_ != nullptr) {
        ESP_LOGW(TAG, "Receive task already running");
        return true;
    }

    BaseType_t ret = xTaskCreate(ReceiveTask, "uart_receive", 4096, this, 5, &receive_task_handle_);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create receive task");
        return false;
    }

    ESP_LOGI(TAG, "UART receive task started");
    return true;
}

void UartCommunication::StopReceiveTask() {
    if (receive_task_handle_ != nullptr) {
        vTaskDelete(receive_task_handle_);
        receive_task_handle_ = nullptr;
        ESP_LOGI(TAG, "UART receive task stopped");
    }
}

void UartCommunication::ReceiveTask(void* pvParameters) {
    UartCommunication* uart_comm = static_cast<UartCommunication*>(pvParameters);
    uart_comm->ProcessReceivedData();
}

void UartCommunication::ProcessReceivedData() {
    uint8_t* data = new uint8_t[buffer_size_];
    std::string received_line;

    while (true) {
        int length = uart_read_bytes(uart_num_, data, buffer_size_ - 1, 100 / portTICK_PERIOD_MS);
        if (length > 0) {
            data[length] = '\0';
            
            // 将接收到的数据添加到行缓冲区
            for (int i = 0; i < length; i++) {
                char c = static_cast<char>(data[i]);
                if (c == '\n' || c == '\r') {
                    // 遇到换行符，处理完整的一行
                    if (!received_line.empty()) {
                        ESP_LOGD(TAG, "Received: %s", received_line.c_str());
                        if (receive_callback_) {
                            receive_callback_(received_line);
                        }
                        received_line.clear();
                    }
                } else if (c >= 32 && c <= 126) {
                    // 只接受可打印字符
                    received_line += c;
                }
            }
        }
        
        // 检查任务是否应该退出
        if (eTaskGetState(receive_task_handle_) == eDeleted) {
            break;
        }
    }

    delete[] data;
    vTaskDelete(NULL);
}
