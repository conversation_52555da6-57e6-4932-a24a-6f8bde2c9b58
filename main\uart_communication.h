#ifndef UART_COMMUNICATION_H
#define UART_COMMUNICATION_H

#include <driver/uart.h>
#include <driver/gpio.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <esp_log.h>

#include <string>
#include <functional>
#include <mutex>

/**
 * UART串口通信类
 * 提供ESP32之间的串口通信功能
 */
class UartCommunication {
public:
    /**
     * 构造函数
     * @param uart_num UART端口号 (UART_NUM_0, UART_NUM_1, UART_NUM_2)
     * @param tx_pin TX引脚
     * @param rx_pin RX引脚
     * @param baud_rate 波特率，默认115200
     * @param buffer_size 接收缓冲区大小，默认1024
     */
    UartCommunication(uart_port_t uart_num, gpio_num_t tx_pin, gpio_num_t rx_pin, 
                     int baud_rate = 115200, int buffer_size = 1024);
    
    /**
     * 析构函数
     */
    ~UartCommunication();

    /**
     * 初始化UART
     * @return true 成功，false 失败
     */
    bool Initialize();

    /**
     * 发送字符串数据
     * @param data 要发送的字符串
     * @return true 成功，false 失败
     */
    bool SendString(const std::string& data);

    /**
     * 发送原始数据
     * @param data 数据指针
     * @param length 数据长度
     * @return true 成功，false 失败
     */
    bool SendData(const uint8_t* data, size_t length);

    /**
     * 设置数据接收回调函数
     * @param callback 回调函数，参数为接收到的字符串
     */
    void SetReceiveCallback(std::function<void(const std::string&)> callback);

    /**
     * 启动接收任务
     * @return true 成功，false 失败
     */
    bool StartReceiveTask();

    /**
     * 停止接收任务
     */
    void StopReceiveTask();

    /**
     * 检查UART是否已初始化
     * @return true 已初始化，false 未初始化
     */
    bool IsInitialized() const { return initialized_; }

private:
    uart_port_t uart_num_;
    gpio_num_t tx_pin_;
    gpio_num_t rx_pin_;
    int baud_rate_;
    int buffer_size_;
    bool initialized_;
    TaskHandle_t receive_task_handle_;
    std::function<void(const std::string&)> receive_callback_;
    std::mutex mutex_;

    /**
     * 接收任务函数
     * @param pvParameters 任务参数
     */
    static void ReceiveTask(void* pvParameters);

    /**
     * 处理接收到的数据
     */
    void ProcessReceivedData();
};

#endif // UART_COMMUNICATION_H
