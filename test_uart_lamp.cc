/**
 * 测试UART灯控制功能的简单程序
 * 这个文件用于验证我们的UART通信和灯控制代码是否正确
 */

#include "main/uart_communication.h"
#include "main/iot/thing_manager.h"
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

#define TAG "TestUartLamp"

// 模拟测试函数
void test_uart_communication() {
    ESP_LOGI(TAG, "Testing UART communication...");
    
    // 创建UART通信实例
    UartCommunication uart(UART_NUM_1, GPIO_NUM_17, GPIO_NUM_18, 115200, 1024);
    
    // 初始化UART
    if (!uart.Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize UART");
        return;
    }
    
    ESP_LOGI(TAG, "UART initialized successfully");
    
    // 设置接收回调
    uart.SetReceiveCallback([](const std::string& data) {
        ESP_LOGI(TAG, "Received: %s", data.c_str());
    });
    
    // 启动接收任务
    if (!uart.StartReceiveTask()) {
        ESP_LOGE(TAG, "Failed to start receive task");
        return;
    }
    
    ESP_LOGI(TAG, "UART receive task started");
    
    // 发送测试命令
    uart.SendString("led turn on");
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    uart.SendString("led turn off");
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    uart.SendString("led status");
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    ESP_LOGI(TAG, "UART communication test completed");
}

void test_iot_integration() {
    ESP_LOGI(TAG, "Testing IoT integration...");
    
    // 这里我们只是验证代码编译正确，不实际运行
    // 因为需要完整的ESP-IDF环境
    
    ESP_LOGI(TAG, "IoT integration test completed");
}

// 主测试函数
extern "C" void app_main() {
    ESP_LOGI(TAG, "Starting UART Lamp tests...");
    
    test_uart_communication();
    test_iot_integration();
    
    ESP_LOGI(TAG, "All tests completed");
    
    // 保持程序运行
    while (true) {
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}
