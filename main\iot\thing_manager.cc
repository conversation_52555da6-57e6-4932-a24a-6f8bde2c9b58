#include "thing_manager.h"

#include <esp_log.h>

#define TAG "ThingManager"

namespace iot {

void ThingManager::AddThing(Thing* thing) {
    things_.push_back(thing);
}

std::string ThingManager::GetDescriptorsJson() {
    std::string json_str = "[";
    for (auto& thing : things_) {
        json_str += thing->GetDescriptorJson() + ",";
    }
    if (json_str.back() == ',') {
        json_str.pop_back();
    }
    json_str += "]";
    return json_str;
}

bool ThingManager::GetStatesJson(std::string& json, bool delta) {
    if (!delta) {
        last_states_.clear();
    }
    bool changed = false;
    json = "[";
    // 枚举thing，获取每个thing的state，如果发生变化，则更新，保存到last_states_
    // 如果delta为true，则只返回变化的部分
    for (auto& thing : things_) {
        std::string state = thing->GetStateJson();
        if (delta) {
            // 如果delta为true，则只返回变化的部分
            auto it = last_states_.find(thing->name());
            if (it != last_states_.end() && it->second == state) {
                continue;
            }
            changed = true;
            last_states_[thing->name()] = state;
        }
        json += state + ",";
    }
    if (json.back() == ',') {
        json.pop_back();
    }
    json += "]";
    return changed;
}

void ThingManager::Invoke(const cJSON* command) {
    // 添加详细的调试日志
    char* command_str = cJSON_Print(command);
    ESP_LOGI(TAG, "ThingManager::Invoke called with command: %s", command_str);
    free(command_str);

    // 尝试获取设备名称，支持多种字段名
    auto name = cJSON_GetObjectItem(command, "name");
    auto thing_id = cJSON_GetObjectItem(command, "thing_id");

    const char* target_name = nullptr;
    if (name && name->valuestring) {
        target_name = name->valuestring;
        ESP_LOGI(TAG, "Using 'name' field: %s", target_name);
    } else if (thing_id && thing_id->valuestring) {
        target_name = thing_id->valuestring;
        ESP_LOGI(TAG, "Using 'thing_id' field: %s", target_name);
    } else {
        ESP_LOGE(TAG, "No valid device name found in command (missing 'name' or 'thing_id' field)");
        return;
    }

    // 打印所有已注册的设备
    ESP_LOGI(TAG, "Searching for device '%s' among %d registered devices:", target_name, things_.size());
    for (size_t i = 0; i < things_.size(); i++) {
        ESP_LOGI(TAG, "  Device %d: '%s'", i + 1, things_[i]->name().c_str());
    }

    // 查找并调用对应设备
    for (auto& thing : things_) {
        if (thing->name() == target_name) {
            ESP_LOGI(TAG, "Found matching device: %s", target_name);
            ESP_LOGI(TAG, "Invoking device method...");
            thing->Invoke(command);
            ESP_LOGI(TAG, "Device method invocation completed");
            return;
        }
    }

    ESP_LOGE(TAG, "Device '%s' not found in registered devices!", target_name);
}

} // namespace iot
