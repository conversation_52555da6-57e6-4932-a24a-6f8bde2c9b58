# ESP32串口控制LED功能说明

## 功能概述

本功能实现了两块ESP32之间通过串口通信来控制LED灯的功能。用户可以通过语音指令"打开灯"、"关闭灯"等来控制另一块ESP32上的LED灯。

## 系统架构

```
[主ESP32 - 小智AI] ←→ UART ←→ [从ESP32 - LED控制器]
```

- **主ESP32**: 运行小智AI语音助手，接收语音指令并通过串口发送控制命令
- **从ESP32**: 接收串口命令并控制LED灯，返回状态信息

## 硬件连接

### 引脚连接
```
主ESP32          从ESP32
GPIO17 (TX) ←→ GPIO18 (RX)
GPIO18 (RX) ←→ GPIO17 (TX)
GND         ←→ GND
```

### LED连接
从ESP32的GPIO2连接LED（大多数开发板的内置LED）

## 软件实现

### 主ESP32 (小智AI端)

1. **UartCommunication类** (`main/uart_communication.h/cc`)
   - 提供UART串口通信的基础功能
   - 支持异步接收和发送
   - 自动处理数据缓冲和行分割

2. **UartLamp类** (`main/iot/things/uart_lamp.cc`)
   - 继承自Thing基类，集成到物联网设备管理系统
   - 实现TurnOn、TurnOff、GetStatus方法
   - 通过串口与从ESP32通信

### 从ESP32 (LED控制端)

1. **uart_led_receiver.cc**
   - 独立的ESP32程序，专门用于接收串口命令
   - 支持的命令：
     - `led turn on` - 打开LED
     - `led turn off` - 关闭LED  
     - `led status` - 查询LED状态
   - 返回响应：
     - `led turned on` - LED已打开
     - `led turned off` - LED已关闭
     - `The led is already on` - LED已经是开启状态
     - `The led is already off` - LED已经是关闭状态

## 语音指令

用户可以使用以下语音指令：
- "打开灯" / "开启灯" / "turn on the light"
- "关闭灯" / "关灯" / "turn off the light"
- "查询灯的状态" / "灯的状态如何"

## 配置说明

### 串口配置
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无
- 流控: 无

### 引脚配置
可以在`main/iot/things/uart_lamp.cc`中修改UART引脚：
```cpp
static constexpr gpio_num_t TX_PIN = GPIO_NUM_17;
static constexpr gpio_num_t RX_PIN = GPIO_NUM_18;
```

## 编译和烧录

### 主ESP32 (小智AI)
1. 确保已添加uart_communication.cc到CMakeLists.txt
2. 在开发板初始化中注册UartLamp设备
3. 正常编译小智AI项目

### 从ESP32 (LED控制器)
1. 创建新的ESP-IDF项目
2. 将uart_led_receiver.cc作为main.cpp
3. 编译并烧录到从ESP32

## 测试步骤

1. **硬件连接**: 按照引脚连接图连接两块ESP32
2. **烧录程序**: 
   - 主ESP32烧录小智AI固件
   - 从ESP32烧录LED控制程序
3. **测试串口通信**: 
   - 可以使用串口调试工具手动发送命令测试
4. **语音测试**: 
   - 启动小智AI
   - 说出"打开灯"等语音指令
   - 观察从ESP32的LED是否响应

## 故障排除

### 常见问题

1. **串口无响应**
   - 检查引脚连接是否正确
   - 确认波特率设置一致
   - 检查GND是否连接

2. **LED不亮**
   - 确认从ESP32的LED引脚配置
   - 检查LED硬件连接
   - 查看从ESP32的串口输出日志

3. **语音识别不响应**
   - 确认UartLamp设备已正确注册
   - 检查物联网设备管理器的日志
   - 验证AI服务器的设备描述

### 调试方法

1. **串口监控**: 使用ESP-IDF的monitor功能查看两块ESP32的日志
2. **手动测试**: 使用串口调试工具手动发送命令
3. **日志分析**: 检查UART通信和物联网设备的日志输出

## 扩展功能

可以基于此框架扩展更多功能：
- 控制多个LED
- 调节LED亮度
- 控制其他设备（风扇、电机等）
- 添加传感器数据回传

## 注意事项

1. 确保两块ESP32的GND连接，否则串口通信可能不稳定
2. 串口引脚不要与其他功能冲突
3. 从ESP32程序要保持简单，专注于命令处理
4. 可以根据实际硬件调整LED引脚配置
