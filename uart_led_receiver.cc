/**
 * 接收端ESP32程序 - 用于接收串口命令并控制LED
 * 这个程序应该烧录到另一块ESP32上，用于接收串口命令并控制LED灯
 */

#include <stdio.h>
#include <string.h>
#include <driver/uart.h>
#include <driver/gpio.h>
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

#define TAG "UartLedReceiver"

// LED引脚配置
#define LED_GPIO GPIO_NUM_2  // 大多数ESP32开发板的内置LED

// UART配置
#define UART_PORT UART_NUM_1
#define TX_PIN GPIO_NUM_17
#define RX_PIN GPIO_NUM_18
#define UART_BUFFER_SIZE 1024

// LED状态
static bool led_state = false;

/**
 * 初始化LED GPIO
 */
void init_led() {
    gpio_config_t config = {
        .pin_bit_mask = (1ULL << LED_GPIO),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    ESP_ERROR_CHECK(gpio_config(&config));
    gpio_set_level(LED_GPIO, 0);  // 初始状态为关闭
    ESP_LOGI(TAG, "LED initialized on GPIO %d", LED_GPIO);
}

/**
 * 初始化UART
 */
void init_uart() {
    uart_config_t uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .rx_flow_ctrl_thresh = 122,
        .source_clk = UART_SCLK_DEFAULT,
    };

    // 安装UART驱动
    ESP_ERROR_CHECK(uart_driver_install(UART_PORT, UART_BUFFER_SIZE * 2, 0, 0, NULL, 0));
    ESP_ERROR_CHECK(uart_param_config(UART_PORT, &uart_config));
    ESP_ERROR_CHECK(uart_set_pin(UART_PORT, TX_PIN, RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));
    
    ESP_LOGI(TAG, "UART initialized (TX: %d, RX: %d)", TX_PIN, RX_PIN);
}

/**
 * 发送响应消息
 */
void send_response(const char* message) {
    uart_write_bytes(UART_PORT, message, strlen(message));
    uart_write_bytes(UART_PORT, "\n", 1);
    ESP_LOGI(TAG, "Sent response: %s", message);
}

/**
 * 处理接收到的命令
 */
void process_command(const char* command) {
    ESP_LOGI(TAG, "Processing command: %s", command);
    
    if (strstr(command, "led turn on") != NULL) {
        if (led_state) {
            send_response("The led is already on");
        } else {
            led_state = true;
            gpio_set_level(LED_GPIO, 1);
            send_response("led turned on");
        }
    } else if (strstr(command, "led turn off") != NULL) {
        if (!led_state) {
            send_response("The led is already off");
        } else {
            led_state = false;
            gpio_set_level(LED_GPIO, 0);
            send_response("led turned off");
        }
    } else if (strstr(command, "led status") != NULL) {
        if (led_state) {
            send_response("The led is already on");
        } else {
            send_response("The led is already off");
        }
    } else {
        send_response("Unknown command");
    }
}

/**
 * UART接收任务
 */
void uart_receive_task(void* pvParameters) {
    uint8_t* data = (uint8_t*)malloc(UART_BUFFER_SIZE);
    char command_buffer[256];
    int command_index = 0;
    
    while (1) {
        int length = uart_read_bytes(UART_PORT, data, UART_BUFFER_SIZE - 1, 100 / portTICK_PERIOD_MS);
        if (length > 0) {
            data[length] = '\0';
            
            // 处理接收到的数据，按行分割
            for (int i = 0; i < length; i++) {
                char c = (char)data[i];
                if (c == '\n' || c == '\r') {
                    if (command_index > 0) {
                        command_buffer[command_index] = '\0';
                        process_command(command_buffer);
                        command_index = 0;
                    }
                } else if (c >= 32 && c <= 126 && command_index < sizeof(command_buffer) - 1) {
                    command_buffer[command_index++] = c;
                }
            }
        }
    }
    
    free(data);
    vTaskDelete(NULL);
}

/**
 * 主函数
 */
extern "C" void app_main() {
    ESP_LOGI(TAG, "UART LED Receiver starting...");
    
    // 初始化LED和UART
    init_led();
    init_uart();
    
    // 创建UART接收任务
    xTaskCreate(uart_receive_task, "uart_receive", 4096, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "UART LED Receiver ready");
    ESP_LOGI(TAG, "Supported commands:");
    ESP_LOGI(TAG, "  - led turn on");
    ESP_LOGI(TAG, "  - led turn off");
    ESP_LOGI(TAG, "  - led status");
    
    // 主循环
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}
