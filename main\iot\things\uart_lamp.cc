#include "iot/thing.h"
#include "uart_communication.h"
#include "board.h"

#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>

#define TAG "UartLamp"

namespace iot {

/**
 * 通过串口控制的灯设备类
 * 与另一块ESP32通过UART通信来控制LED灯
 */
class UartLamp : public Thing {
private:
    UartCommunication* uart_comm_;
    bool power_;
    bool waiting_for_response_;
    SemaphoreHandle_t response_semaphore_;
    std::string last_response_;
    
    // UART配置 - 选择未被占用的引脚
#ifdef CONFIG_IDF_TARGET_ESP32
    static constexpr uart_port_t UART_PORT = UART_NUM_1;
    static constexpr gpio_num_t TX_PIN = GPIO_NUM_17;  // ESP32的TX引脚
    static constexpr gpio_num_t RX_PIN = GPIO_NUM_18;  // ESP32的RX引脚
#else
    static constexpr uart_port_t UART_PORT = UART_NUM_1;
    static constexpr gpio_num_t TX_PIN = GPIO_NUM_44;  // ESP32-S3的TX引脚
    static constexpr gpio_num_t RX_PIN = GPIO_NUM_43;  // ESP32-S3的RX引脚
#endif

    /**
     * 初始化UART通信
     */
    void InitializeUart() {
        uart_comm_ = new UartCommunication(UART_PORT, TX_PIN, RX_PIN, 115200, 1024);
        
        if (!uart_comm_->Initialize()) {
            ESP_LOGE(TAG, "Failed to initialize UART communication");
            delete uart_comm_;
            uart_comm_ = nullptr;
            return;
        }

        // 设置接收回调函数
        uart_comm_->SetReceiveCallback([this](const std::string& data) {
            OnUartDataReceived(data);
        });

        // 启动接收任务
        if (!uart_comm_->StartReceiveTask()) {
            ESP_LOGE(TAG, "Failed to start UART receive task");
        }

        ESP_LOGI(TAG, "UART communication initialized (TX: %d, RX: %d)", TX_PIN, RX_PIN);
    }

    /**
     * 处理UART接收到的数据
     * @param data 接收到的数据
     */
    void OnUartDataReceived(const std::string& data) {
        ESP_LOGI(TAG, "Received from UART: %s", data.c_str());
        
        // 保存最后的响应
        last_response_ = data;
        
        // 解析响应并更新灯的状态
        if (data.find("The led is on") != std::string::npos) {
            power_ = true;
            ESP_LOGI(TAG, "LED confirmed ON");
        } else if (data.find("The led is off") != std::string::npos) {
            power_ = false;
            ESP_LOGI(TAG, "LED confirmed OFF");
        } else if (data.find("led turn on") != std::string::npos) {
            power_ = true;
            ESP_LOGI(TAG, "LED turned ON");
        } else if (data.find("led turn off") != std::string::npos) {
            power_ = false;
            ESP_LOGI(TAG, "LED turned OFF");
        }
        
        // 如果正在等待响应，释放信号量
        if (waiting_for_response_) {
            waiting_for_response_ = false;
            xSemaphoreGive(response_semaphore_);
        }
    }

    /**
     * 发送命令并等待响应
     * @param command 要发送的命令
     * @param timeout_ms 超时时间（毫秒）
     * @return true 成功，false 失败或超时
     */
    bool SendCommandAndWaitResponse(const std::string& command, int timeout_ms = 3000) {
        if (!uart_comm_ || !uart_comm_->IsInitialized()) {
            ESP_LOGE(TAG, "UART not initialized");
            return false;
        }

        // 清除之前的响应
        last_response_.clear();
        waiting_for_response_ = true;

        // 发送命令
        ESP_LOGI(TAG, "Sending command: %s", command.c_str());
        if (!uart_comm_->SendString(command + "\n")) {
            ESP_LOGE(TAG, "Failed to send command");
            waiting_for_response_ = false;
            return false;
        }

        // 等待响应
        if (xSemaphoreTake(response_semaphore_, pdMS_TO_TICKS(timeout_ms)) == pdTRUE) {
            ESP_LOGI(TAG, "Received response: %s", last_response_.c_str());
            return true;
        } else {
            ESP_LOGW(TAG, "Timeout waiting for response");
            waiting_for_response_ = false;
            return false;
        }
    }

public:
    UartLamp() : Thing("UartLamp", "通过串口控制的灯"), 
                 uart_comm_(nullptr), power_(false), waiting_for_response_(false) {
        
        // 创建响应信号量
        response_semaphore_ = xSemaphoreCreateBinary();
        if (response_semaphore_ == NULL) {
            ESP_LOGE(TAG, "Failed to create response semaphore");
            return;
        }

        // 初始化UART通信
        InitializeUart();

        // 定义设备的属性
        properties_.AddBooleanProperty("power", "灯是否打开", [this]() -> bool {
            return power_;
        });

        // 定义设备可以被远程执行的指令
        methods_.AddMethod("TurnOn", "打开灯", ParameterList(), [this](const ParameterList& parameters) {
            ESP_LOGI(TAG, "Executing TurnOn command");
            if (SendCommandAndWaitResponse("led turn on")) {
                ESP_LOGI(TAG, "LED turn on command completed");
            } else {
                ESP_LOGW(TAG, "LED turn on command failed or timeout");
            }
        });

        methods_.AddMethod("TurnOff", "关闭灯", ParameterList(), [this](const ParameterList& parameters) {
            ESP_LOGI(TAG, "Executing TurnOff command");
            if (SendCommandAndWaitResponse("led turn off")) {
                ESP_LOGI(TAG, "LED turn off command completed");
            } else {
                ESP_LOGW(TAG, "LED turn off command failed or timeout");
            }
        });

        // 添加状态查询方法
        methods_.AddMethod("GetStatus", "查询灯的状态", ParameterList(), [this](const ParameterList& parameters) {
            ESP_LOGI(TAG, "Executing GetStatus command");
            if (SendCommandAndWaitResponse("led status")) {
                ESP_LOGI(TAG, "LED status query completed");
            } else {
                ESP_LOGW(TAG, "LED status query failed or timeout");
            }
        });

        ESP_LOGI(TAG, "UartLamp device initialized");
    }

    ~UartLamp() {
        if (uart_comm_) {
            delete uart_comm_;
        }
        if (response_semaphore_) {
            vSemaphoreDelete(response_semaphore_);
        }
    }
};

} // namespace iot

DECLARE_THING(UartLamp);
